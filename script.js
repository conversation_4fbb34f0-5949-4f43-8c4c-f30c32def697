// Global variables to store reCAPTCHA widget IDs
let recaptchaDarkId = null;
let recaptchaLightId = null;

// Function to get current theme
function getCurrentTheme() {
    const htmlElement = document.documentElement;
    return htmlElement.classList.contains('dark') ? 'dark' : 'light';
}

// Function to initialize both reCAPTCHA widgets
function initializeRecaptcha() {
    console.log('Attempting to initialize reCAPTCHA...');
    const recaptchaContainer = document.getElementById('recaptcha-widget');
    if (recaptchaContainer && typeof grecaptcha !== 'undefined' && grecaptcha.render) {
        try {
            // Create containers for both themes
            recaptchaContainer.innerHTML = `
                <div id="recaptcha-dark" style="display: block;"></div>
                <div id="recaptcha-light" style="display: none;"></div>
            `;

            // Render both widgets
            recaptchaDarkId = grecaptcha.render('recaptcha-dark', {
                'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
                'theme': 'dark'
            });

            recaptchaLightId = grecaptcha.render('recaptcha-light', {
                'sitekey': '6LdLmHMrAAAAAOjVgSLRiO4bmQx2T07gWNT01nT0',
                'theme': 'light'
            });

            console.log('reCAPTCHA widgets rendered successfully');

            // Show the correct widget based on current theme
            switchRecaptchaTheme();
        } catch (error) {
            console.error('Error initializing reCAPTCHA:', error);
        }
    } else {
        console.log('reCAPTCHA not ready yet');
    }
}

// Function to switch between reCAPTCHA themes
function switchRecaptchaTheme() {
    const darkWidget = document.getElementById('recaptcha-dark');
    const lightWidget = document.getElementById('recaptcha-light');
    const currentTheme = getCurrentTheme();

    if (darkWidget && lightWidget) {
        if (currentTheme === 'dark') {
            darkWidget.style.display = 'block';
            lightWidget.style.display = 'none';
        } else {
            darkWidget.style.display = 'none';
            lightWidget.style.display = 'block';
        }
    }
}

// Function called when reCAPTCHA API loads
function onRecaptchaLoad() {
    console.log('reCAPTCHA API loaded, initializing...');
    initializeRecaptcha();
}

// Also set it on window object for compatibility
window.onRecaptchaLoad = onRecaptchaLoad;

// Create a mock reCAPTCHA for local development
function createMockRecaptcha() {
    console.log('Creating mock reCAPTCHA for local development...');
    const recaptchaContainer = document.getElementById('recaptcha-widget');
    if (recaptchaContainer) {
        recaptchaContainer.innerHTML = `
            <div style="
                width: 304px;
                height: 78px;
                border: 1px solid #d3d3d3;
                background: #f9f9f9;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: Arial, sans-serif;
                color: #666;
                border-radius: 3px;
                margin: 0 auto;
            ">
                <div style="display: flex; align-items: center;">
                    <input type="checkbox" id="mock-recaptcha" style="margin-right: 10px; transform: scale(1.2);">
                    <label for="mock-recaptcha">I'm not a robot (Local Dev Mode)</label>
                </div>
            </div>
        `;

        // Set mock IDs to indicate it's working
        recaptchaDarkId = 'mock-dark';
        recaptchaLightId = 'mock-light';
    }
}

// Fallback initialization in case onRecaptchaLoad doesn't fire
function tryInitializeRecaptcha() {
    console.log('Trying fallback reCAPTCHA initialization...');
    if (typeof grecaptcha !== 'undefined' && grecaptcha.render && recaptchaDarkId === null) {
        console.log('Fallback reCAPTCHA initialization');
        initializeRecaptcha();
    } else if (recaptchaDarkId === null) {
        console.log('reCAPTCHA API not available, using mock for local development');
        createMockRecaptcha();
    }
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded');

    // Try to initialize reCAPTCHA if it hasn't been initialized yet (fallback)
    setTimeout(tryInitializeRecaptcha, 3000);

    // Initialize particles.js for hero section
    console.log('Particles.js available:', typeof particlesJS !== 'undefined');
    if (typeof particlesJS !== 'undefined') {
        console.log('Initializing particles...');
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": "#007db6"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 10,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.5,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#007db6",
                    "opacity": 0.6,
                    "width": 3
                },
                "move": {
                    "enable": true,
                    "speed": 6,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "repulse"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 400,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        }, function() {
            console.log('Particles.js loaded successfully!');
        });
    } else {
        console.log('Particles.js not found!');
    }

    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const htmlElement = document.documentElement;
    
    // Check for saved theme preference or use default dark theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        htmlElement.className = savedTheme;
    } else {
        // Default to dark theme if no preference is saved
        htmlElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
    
    themeToggle.addEventListener('click', () => {
        if (htmlElement.classList.contains('dark')) {
            htmlElement.classList.remove('dark');
            localStorage.setItem('theme', '');
        } else {
            htmlElement.classList.add('dark');
            localStorage.setItem('theme', 'dark');
        }

        // Switch reCAPTCHA theme
        switchRecaptchaTheme();
    });
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
        });
    });
    
    // Form submission handling
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // Check if reCAPTCHA is completed (check the currently visible widget)
            const currentTheme = getCurrentTheme();
            const currentWidgetId = currentTheme === 'dark' ? recaptchaDarkId : recaptchaLightId;

            console.log('Form submission - Current theme:', currentTheme);
            console.log('Form submission - Widget ID:', currentWidgetId);
            console.log('Form submission - reCAPTCHA available:', typeof grecaptcha !== 'undefined');

            let recaptchaResponse = '';

            // Handle mock reCAPTCHA for local development
            if (currentWidgetId === 'mock-dark' || currentWidgetId === 'mock-light') {
                const mockCheckbox = document.getElementById('mock-recaptcha');
                if (mockCheckbox && mockCheckbox.checked) {
                    recaptchaResponse = 'mock-response-for-local-dev';
                    console.log('Mock reCAPTCHA verified');
                }
            } else if (currentWidgetId !== null && typeof grecaptcha !== 'undefined' && grecaptcha.getResponse) {
                try {
                    recaptchaResponse = grecaptcha.getResponse(currentWidgetId);
                    console.log('reCAPTCHA response:', recaptchaResponse ? 'Present' : 'Empty');
                } catch (error) {
                    console.error('Error getting reCAPTCHA response:', error);
                }
            }

            if (!recaptchaResponse) {
                alert('Please complete the reCAPTCHA verification.');
                return;
            }

            // In a real implementation, you would send the form data to a server
            // For this example, we'll just show a success message
            const formData = new FormData(contactForm);
            let formValues = {};

            for (let [key, value] of formData.entries()) {
                formValues[key] = value;
            }

            // Add reCAPTCHA response to form data
            formValues['g-recaptcha-response'] = recaptchaResponse;

            console.log('Form submitted:', formValues);

            // Reset form and show success message
            contactForm.reset();

            // Reset reCAPTCHA widgets
            if (recaptchaDarkId === 'mock-dark' || recaptchaLightId === 'mock-light') {
                // Reset mock reCAPTCHA
                const mockCheckbox = document.getElementById('mock-recaptcha');
                if (mockCheckbox) {
                    mockCheckbox.checked = false;
                }
            } else {
                // Reset real reCAPTCHA widgets
                if (recaptchaDarkId !== null && typeof grecaptcha !== 'undefined') {
                    grecaptcha.reset(recaptchaDarkId);
                }
                if (recaptchaLightId !== null && typeof grecaptcha !== 'undefined') {
                    grecaptcha.reset(recaptchaLightId);
                }
            }

            alert('Thank you for your message! We will get back to you soon.');
        });
    }


    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('nav ul');

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a nav link
    document.querySelectorAll('nav ul li a').forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                navMenu.classList.remove('active');
            }
        });
    });

    // Video testimonials functionality
    const videoModal = document.getElementById('video-modal');
    const modalVideo = document.getElementById('modal-video');
    const modalTitle = document.getElementById('video-modal-title');
    const modalClose = document.querySelector('.video-modal-close');
    const videoTestimonials = document.querySelectorAll('.video-testimonial');

    // Set background images for video thumbnails
    videoTestimonials.forEach(testimonial => {
        const thumbnailSrc = testimonial.getAttribute('data-thumbnail');
        const videoThumbnail = testimonial.querySelector('.video-thumbnail');
        if (thumbnailSrc && videoThumbnail) {
            videoThumbnail.style.backgroundImage = `linear-gradient(135deg, rgba(0,125,182,0.4), rgba(0,93,133,0.6)), url('${thumbnailSrc}')`;
        }
    });

    // Preload video testimonials in the background after page load
    const preloadedVideos = new Map();

    function preloadVideoTestimonials() {
        console.log('Starting background preload of video testimonials...');

        videoTestimonials.forEach((testimonial, index) => {
            const videoSrc = testimonial.getAttribute('data-video');
            const videoName = testimonial.getAttribute('data-name');

            if (videoSrc && !preloadedVideos.has(videoSrc)) {
                // Create a hidden video element for preloading
                const preloadVideo = document.createElement('video');
                preloadVideo.style.display = 'none';
                preloadVideo.preload = 'auto';
                preloadVideo.muted = true; // Muted videos can autoload without user interaction

                // Add event listeners to track loading progress
                preloadVideo.addEventListener('loadstart', () => {
                    console.log(`Started loading ${videoName}'s testimonial`);
                });

                preloadVideo.addEventListener('canplaythrough', () => {
                    console.log(`${videoName}'s testimonial fully loaded and ready to play`);
                });

                preloadVideo.addEventListener('error', (e) => {
                    console.error(`Error preloading ${videoName}'s testimonial:`, e);
                });

                // Set the source and start preloading
                preloadVideo.src = videoSrc;

                // Store the preloaded video
                preloadedVideos.set(videoSrc, preloadVideo);

                // Add to DOM (hidden) to start loading
                document.body.appendChild(preloadVideo);

                // Stagger the loading to avoid overwhelming the connection
                setTimeout(() => {
                    preloadVideo.load();
                }, index * 2000); // 2 second delay between each video
            }
        });
    }

    // Start preloading after a short delay to ensure page is fully loaded
    setTimeout(preloadVideoTestimonials, 3000);

    // Open video modal
    videoTestimonials.forEach(testimonial => {
        testimonial.addEventListener('click', () => {
            const videoSrc = testimonial.getAttribute('data-video');
            const videoName = testimonial.getAttribute('data-name');

            // Check if we have a preloaded version
            const preloadedVideo = preloadedVideos.get(videoSrc);

            if (preloadedVideo && preloadedVideo.readyState >= 3) {
                // Use preloaded video for faster playback
                console.log(`Using preloaded video for ${videoName}`);

                // Copy the preloaded video's current time and other properties
                modalVideo.src = videoSrc;
                modalVideo.currentTime = 0; // Start from beginning

                // The video should start much faster since it's preloaded
                modalTitle.textContent = `${videoName}'s Testimonial`;
                videoModal.style.display = 'block';

                // Auto-play the video when modal opens
                modalVideo.play().catch(error => {
                    console.log('Autoplay was prevented:', error);
                });
            } else {
                // Fallback to normal loading
                console.log(`Loading video normally for ${videoName}`);
                modalVideo.src = videoSrc;
                modalTitle.textContent = `${videoName}'s Testimonial`;
                videoModal.style.display = 'block';

                // Auto-play the video when modal opens
                modalVideo.play().catch(error => {
                    console.log('Autoplay was prevented:', error);
                });
            }

            // Prevent body scrolling when modal is open
            document.body.style.overflow = 'hidden';
        });
    });

    // Close video modal
    function closeVideoModal() {
        videoModal.style.display = 'none';
        modalVideo.pause();
        modalVideo.src = '';
        document.body.style.overflow = 'auto';

        // Optional: Clean up preloaded videos after some time to free memory
        // This is commented out to keep videos cached for better UX
        // setTimeout(() => {
        //     preloadedVideos.forEach((video, src) => {
        //         document.body.removeChild(video);
        //     });
        //     preloadedVideos.clear();
        // }, 300000); // Clean up after 5 minutes
    }

    // Close modal when clicking close button
    modalClose.addEventListener('click', closeVideoModal);

    // Close modal when clicking outside the video content
    videoModal.addEventListener('click', (e) => {
        if (e.target === videoModal) {
            closeVideoModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && videoModal.style.display === 'block') {
            closeVideoModal();
        }
    });

    // Handle fullscreen functionality
    modalVideo.addEventListener('dblclick', () => {
        if (modalVideo.requestFullscreen) {
            modalVideo.requestFullscreen();
        } else if (modalVideo.webkitRequestFullscreen) {
            modalVideo.webkitRequestFullscreen();
        } else if (modalVideo.msRequestFullscreen) {
            modalVideo.msRequestFullscreen();
        }
    });
});

